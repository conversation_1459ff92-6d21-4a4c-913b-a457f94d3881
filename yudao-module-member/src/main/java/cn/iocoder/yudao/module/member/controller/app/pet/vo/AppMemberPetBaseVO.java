package cn.iocoder.yudao.module.member.controller.app.pet.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 用户宠物 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class AppMemberPetBaseVO {

    @Schema(description = "头像", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/xxx.png")
    @NotEmpty(message = "头像不能为空")
    private String avatar;

    @Schema(description = "真实名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "旺财")
    @NotEmpty(message = "真实名字不能为空")
    private String name;

    @Schema(description = "用户性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "用户性别不能为空")
    private Integer sex;

    @Schema(description = "出生日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-01-01")
    @NotNull(message = "出生日期不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private Date birthday;

    @Schema(description = "品种", requiredMode = Schema.RequiredMode.REQUIRED, example = "金毛")
    @NotEmpty(message = "品种不能为空")
    private String breed;

    @Schema(description = "技能", requiredMode = Schema.RequiredMode.REQUIRED, example = "握手")
    @NotEmpty(message = "技能不能为空")
    private String skill;

    @Schema(description = "主食", example = "狗粮")
    private String food;

    @Schema(description = "零食", example = "肉干")
    private String snacks;

}