package cn.iocoder.yudao.module.member.dal.mysql.pet;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.member.dal.dataobject.pet.MemberPetDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户宠物 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberPetMapper extends BaseMapperX<MemberPetDO> {

    default List<MemberPetDO> selectListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<MemberPetDO>()
                .eq(MemberPetDO::getUserId, userId));
    }

}