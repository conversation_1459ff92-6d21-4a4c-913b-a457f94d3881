package cn.iocoder.yudao.module.member.service.pet;

import cn.iocoder.yudao.module.member.controller.app.pet.vo.AppMemberPetCreateReqVO;
import cn.iocoder.yudao.module.member.controller.app.pet.vo.AppMemberPetUpdateReqVO;
import cn.iocoder.yudao.module.member.dal.dataobject.pet.MemberPetDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户宠物 Service 接口
 */
public interface MemberPetService {

    /**
     * 创建用户宠物
     *
     * @param userId 用户ID
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPet(Long userId, @Valid AppMemberPetCreateReqVO createReqVO);

    /**
     * 更新用户宠物
     *
     * @param userId 用户ID
     * @param updateReqVO 更新信息
     */
    void updatePet(Long userId, @Valid AppMemberPetUpdateReqVO updateReqVO);

    /**
     * 删除用户宠物
     *
     * @param userId 用户ID
     * @param id 编号
     */
    void deletePet(Long userId, Long id);

    /**
     * 获得用户宠物
     *
     * @param userId 用户ID
     * @param id 编号
     * @return 用户宠物
     */
    MemberPetDO getPet(Long userId, Long id);

    /**
     * 获得用户宠物列表
     *
     * @param userId 用户ID
     * @return 用户宠物列表
     */
    List<MemberPetDO> getPetList(Long userId);

}