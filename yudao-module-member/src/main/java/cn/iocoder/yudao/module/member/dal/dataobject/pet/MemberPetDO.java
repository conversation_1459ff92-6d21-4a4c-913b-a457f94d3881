package cn.iocoder.yudao.module.member.dal.dataobject.pet;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 用户宠物 DO
 *
 * <AUTHOR>
 */
@TableName("member_pet")
@KeySequence("member_pet_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberPetDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 真实名字
     */
    private String name;
    /**
     * 用户性别
     */
    private Integer sex;
    /**
     * 出生日期
     */
    private Date birthday;
    /**
     * 品种
     */
    private String breed;
    /**
     * 技能
     */
    private String skill;
    /**
     * 主食
     */
    private String food;
    /**
     * 零食
     */
    private String snacks;

}