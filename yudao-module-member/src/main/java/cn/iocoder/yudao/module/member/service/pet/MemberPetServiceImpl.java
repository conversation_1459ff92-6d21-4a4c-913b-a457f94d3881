package cn.iocoder.yudao.module.member.service.pet;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.member.controller.app.pet.vo.AppMemberPetCreateReqVO;
import cn.iocoder.yudao.module.member.controller.app.pet.vo.AppMemberPetUpdateReqVO;
import cn.iocoder.yudao.module.member.dal.dataobject.pet.MemberPetDO;
import cn.iocoder.yudao.module.member.dal.mysql.pet.MemberPetMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.member.enums.ErrorCodeConstants.PET_NOT_EXISTS;

/**
 * 用户宠物 Service 实现类
 */
@Service
@Validated
public class MemberPetServiceImpl implements MemberPetService {

    @Resource
    private MemberPetMapper petMapper;

    @Override
    public Long createPet(Long userId, @Valid AppMemberPetCreateReqVO createReqVO) {
        // 插入
        MemberPetDO pet = BeanUtils.toBean(createReqVO, MemberPetDO.class);
        pet.setUserId(userId);
        petMapper.insert(pet);
        // 返回
        return pet.getId();
    }

    @Override
    public void updatePet(Long userId, @Valid AppMemberPetUpdateReqVO updateReqVO) {
        // 校验存在
        validatePetExists(userId, updateReqVO.getId());
        // 更新
        MemberPetDO updateObj = BeanUtils.toBean(updateReqVO, MemberPetDO.class);
        updateObj.setUserId(null); // 避免更新 userId
        petMapper.updateById(updateObj);
    }

    @Override
    public void deletePet(Long userId, Long id) {
        // 校验存在
        validatePetExists(userId, id);
        // 删除
        petMapper.deleteById(id);
    }

    private void validatePetExists(Long userId, Long id) {
        MemberPetDO pet = petMapper.selectById(id);
        if (pet == null) {
            throw exception(PET_NOT_EXISTS);
        }
        if (!pet.getUserId().equals(userId)) {
            throw exception(PET_NOT_EXISTS);
        }
    }

    @Override
    public MemberPetDO getPet(Long userId, Long id) {
        MemberPetDO pet = petMapper.selectById(id);
        if (pet == null) {
            throw exception(PET_NOT_EXISTS);
        }
        if (!pet.getUserId().equals(userId)) {
            throw exception(PET_NOT_EXISTS);
        }
        return pet;
    }

    @Override
    public List<MemberPetDO> getPetList(Long userId) {
        return petMapper.selectListByUserId(userId);
    }

}