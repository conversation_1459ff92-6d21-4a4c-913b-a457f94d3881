package cn.iocoder.yudao.module.member.controller.app.pet;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.member.controller.app.pet.vo.AppMemberPetCreateReqVO;
import cn.iocoder.yudao.module.member.controller.app.pet.vo.AppMemberPetRespVO;
import cn.iocoder.yudao.module.member.controller.app.pet.vo.AppMemberPetUpdateReqVO;
import cn.iocoder.yudao.module.member.convert.pet.MemberPetConvert;
import cn.iocoder.yudao.module.member.dal.dataobject.pet.MemberPetDO;
import cn.iocoder.yudao.module.member.service.pet.MemberPetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 用户宠物")
@RestController
@RequestMapping("/member/pet")
@Validated
public class AppMemberPetController {

    @Resource
    private MemberPetService petService;

    @PostMapping("/create")
    @Operation(summary = "创建用户宠物")
    public CommonResult<Long> createPet(@Valid @RequestBody AppMemberPetCreateReqVO createReqVO) {
        return success(petService.createPet(getLoginUserId(), createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户宠物")
    public CommonResult<Boolean> updatePet(@Valid @RequestBody AppMemberPetUpdateReqVO updateReqVO) {
        petService.updatePet(getLoginUserId(), updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户宠物")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deletePet(@RequestParam("id") Long id) {
        petService.deletePet(getLoginUserId(), id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户宠物")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppMemberPetRespVO> getPet(@RequestParam("id") Long id) {
        MemberPetDO pet = petService.getPet(getLoginUserId(), id);
        return success(MemberPetConvert.INSTANCE.convert(pet));
    }

    @GetMapping("/list")
    @Operation(summary = "获得用户宠物列表")
    public CommonResult<List<AppMemberPetRespVO>> getPetList() {
        List<MemberPetDO> list = petService.getPetList(getLoginUserId());
        return success(MemberPetConvert.INSTANCE.convertList(list));
    }

}