package cn.iocoder.yudao.module.member.convert.pet;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.controller.app.pet.vo.AppMemberPetCreateReqVO;
import cn.iocoder.yudao.module.member.controller.app.pet.vo.AppMemberPetRespVO;
import cn.iocoder.yudao.module.member.controller.app.pet.vo.AppMemberPetUpdateReqVO;
import cn.iocoder.yudao.module.member.dal.dataobject.pet.MemberPetDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberPetConvert {

    MemberPetConvert INSTANCE = Mappers.getMapper(MemberPetConvert.class);

    MemberPetDO convert(AppMemberPetCreateReqVO bean);

    MemberPetDO convert(AppMemberPetUpdateReqVO bean);

    AppMemberPetRespVO convert(MemberPetDO bean);

    List<AppMemberPetRespVO> convertList(List<MemberPetDO> list);

    PageResult<AppMemberPetRespVO> convertPage(PageResult<MemberPetDO> page);


}